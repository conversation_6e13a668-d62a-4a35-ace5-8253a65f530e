"use client";

import logoImage from "@/../public/logo.png";
import { Button } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { useAuthStore } from "@/store/auth";
import { useAuthDialogStore } from "@/store/authDialogStore";
import {
  Info,
  LogIn,
  LogOut,
  Menu,
  Settings,
  User,
  UserPlus
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import messages from "../../../messages/en.json";
import { mainNavItems } from "./Header";

export default function MobileMenu() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const tNav = useTranslations("nav");
  const tAuth = useTranslations("auth");
  const language = useLocale();
  const isRTL = language === "ar" ? true : false;
  const openAuthDialog = useAuthDialogStore((state) => state.openDialog);
  const { user, logout } = useAuthStore();
  const isAuthenticated = !!user;

  return (
    <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="p-2">
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent
        side={language === "ar" ? "left" : "right"}
        className="bg-background w-80 border p-0"
      >
        <div className="border-b p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Image
                src={logoImage}
                alt="Hala Logo"
                className="h-10 w-10 object-contain"
              />
              <span className="sr-only ml-3 text-lg font-bold">Hala</span>
            </div>
          </div>
        </div>

        <nav className="flex flex-col space-y-1 p-6">
          {mainNavItems.map(({ key, icon: Icon, link }) => (
            <Link key={key} href={link || "#"}>
              <Button
                variant="ghost"
                className="w-full justify-start rounded-lg px-4 py-3 text-left transition-colors hover:bg-gray-50"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Icon className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"} `} />
                <span className="">
                  {tNav(`${key as keyof typeof messages.nav}`)}
                </span>
              </Button>
            </Link>
          ))}

          <Button
            variant="ghost"
            className="w-full justify-start rounded-lg px-4 py-3 text-left transition-colors hover:bg-gray-50"
          >
            <Info className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"} `} />
            <span className="t">{tNav("about")}</span>
          </Button>

          <div className="mt-4 space-y-2 border-t pt-4">
            {isAuthenticated ? (
              <>
                <div className="flex items-center px-4 py-3">
                  {user?.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="h-8 w-8 rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-5 w-5 text-gray-500" />
                  )}
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      {user?.name}
                    </p>
                    <p className="text-xs text-gray-500">{user?.email}</p>
                  </div>
                </div>
                <Link href="/profile">
                  <Button
                    variant="ghost"
                    className="w-full justify-start rounded-lg px-4 py-3 text-left transition-colors hover:bg-gray-50"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <User className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"}`} />
                    <span className="">{tAuth("profile")}</span>
                  </Button>
                </Link>
                <Button
                  variant="ghost"
                  className="w-full justify-start rounded-lg px-4 py-3 text-left transition-colors hover:bg-gray-50"
                >
                  <Settings className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"}`} />
                  <span className="">{tAuth("settings")}</span>
                </Button>
                <Button
                  onClick={logout}
                  variant="ghost"
                  className="w-full justify-start rounded-lg px-4 py-3 text-left text-red-600 transition-colors hover:bg-red-50"
                >
                  <LogOut className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"}`} />
                  <span>{tAuth("signOut")}</span>
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  className="w-full justify-start rounded-lg px-4 py-3 text-left transition-colors hover:bg-gray-50"
                  onClick={() => {
                    openAuthDialog("signin");
                    setIsMobileMenuOpen(false);
                  }}
                >
                  <LogIn className={`h-5 w-5 ${isRTL ? "ml-3" : "mr-3"}`} />
                  <span className="">{tNav("login")}</span>
                </Button>
                <Button
                  className="w-full rounded-lg bg-[#279fc7] py-3 text-white shadow-sm transition-colors hover:bg-[#279fc7]/90"
                  onClick={() => {
                    openAuthDialog("signup");
                    setIsMobileMenuOpen(false);
                  }}
                >
                  <UserPlus className={`h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {tNav("signup")}
                </Button>
              </>
            )}
          </div>
        </nav>
      </SheetContent>
    </Sheet>
  );
}
