import { useTranslations } from "next-intl";
import { ContactForm } from "./ContactForm";

const CTASection: React.FC = () => {
  const t = useTranslations("corporate");

  return (
    <section className="relative overflow-hidden bg-[#279fc7] py-20">
      <div className="absolute inset-0 bg-gradient-to-br from-[#279fc7] to-[#1e7a9a]"></div>
      <div className="relative mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
        <h2 className="animate-fade-in mb-6 text-3xl font-bold text-white md:text-4xl">
          {t("cta.title")}
        </h2>
        <p className="animate-fade-in-delay mx-auto mb-8 max-w-3xl text-lg text-blue-100">
          {t("cta.description")}
        </p>
        <ContactForm />
      </div>
    </section>
  );
};

export default CTASection;
