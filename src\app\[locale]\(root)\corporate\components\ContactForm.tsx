"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useCreateCorporate } from "@/hooks/corporates";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import {
  ArrowRight,
  Building2,
  Link,
  Mail,
  MessageSquare,
  Phone,
  Send,
  User,
} from "lucide-react";
import { useTranslations } from "next-intl";

const formSchema = z.object({
  fullName: z
    .string()
    .min(1, {
      message: "*",
    })
    .max(100, {
      message: "Name cannot exceed 100 characters.",
    }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phone: z.string().regex(/^\d{10,15}$/, {
    message: "Phone number must be between 10 and 15 digits.",
  }),
  company: z
    .string()
    .min(2, {
      message: "*",
    })
    .max(100, {
      message: "Company name cannot exceed 100 characters.",
    }),
  websiteOrLinkedIn: z
    .string()
    .optional()
    .refine((val) => val?.startsWith("http") || val?.startsWith("https"), {
      message: "Please enter a valid URL.",
    })
    .optional(),
  message: z
    .string()
    .min(5, {
      message: "Message must be at least 5 characters.",
    })
    .max(1000, {
      message: "Message cannot exceed 1000 characters.",
    }),
});

export function ContactForm() {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const { mutate: createCorporate, isPending, error } = useCreateCorporate();

  console.log("Error", error);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      company: "",
      websiteOrLinkedIn: "",
      message: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    createCorporate(values, {
      onSuccess: () => {
        form.reset();
        setIsOpen(false);
      },
    });
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          form.reset();
        }
        setIsOpen(open);
      }}
    >
      <DialogTrigger asChild>
        <Button
          size="lg"
          className="group bg-white px-8 py-3 text-lg font-semibold text-[#279fc7] shadow-lg transition-all duration-300 hover:scale-105 hover:bg-gray-100 hover:shadow-xl"
        >
          {t("corporate.cta.button")}
          <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
        </Button>
      </DialogTrigger>
      <DialogContent showCloseButton={false} className="p-6 sm:max-w-[600px]">
        <DialogHeader>
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold">
              {t("contact.form.title")}
            </DialogTitle>
            <DialogDescription className="text-center text-gray-600">
              {t("contact.form.description")}
            </DialogDescription>
          </DialogHeader>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {" "}
                      <User className="mr-2 h-4 w-4 text-gray-500" />
                      {t("contact.form.name")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("contact.form.namePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="company"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Building2 className="mr-2 h-4 w-4 text-gray-500" />
                      {t("contact.form.companyName")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("contact.form.companyNamePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="">
                    <FormLabel>
                      {" "}
                      <Mail className="mr-2 h-4 w-4 text-gray-500" />
                      {t("contact.form.email")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("contact.form.emailPlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <Phone className="mr-2 h-4 w-4 text-gray-500" />
                      {t("contact.form.phone")}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("contact.form.phonePlaceholder")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="websiteOrLinkedIn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {" "}
                    <Link className="mr-2 h-4 w-4 text-gray-500" />
                    {t("contact.form.companyWebsite")}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("contact.form.companyWebsitePlaceholder")}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <MessageSquare className="mr-2 h-4 w-4 text-gray-500" />
                    {t("contact.form.message")}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t("contact.form.messagePlaceholder")}
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button className="w-full" type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                  {t("contact.form.sending")}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  {t("contact.form.submit")}
                </>
              )}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
