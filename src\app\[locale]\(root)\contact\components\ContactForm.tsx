/* eslint-disable no-unused-vars */
"use client";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import { Send } from "lucide-react";

interface FormData {
  name: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

const ContactForm: React.FC = () => {
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      toast.success(t("contact.form.success"));
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
      });
    } catch (error) {
      toast.error(t("contact.form.error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <Card className="shadow-elevated bg-background overflow-hidden border-0">
        <CardContent className="p-8">
          <div className="mb-8 text-center">
            <div className="from-primary to-brand-ocean mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r">
              <Send className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-foreground mb-2 text-2xl font-bold">
              {t("contact.form.title")}
            </h3>
            <p className="text-muted-foreground">
              {t("contact.form.description")}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-foreground mb-2 block text-sm font-medium">
                  {t("contact.form.name")}
                </label>
                <Input
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder={t("contact.form.namePlaceholder")}
                  className="h-12"
                  required
                />
              </div>
              <div>
                <label className="text-foreground mb-2 block text-sm font-medium">
                  {t("contact.form.email")}
                </label>
                <Input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder={t("contact.form.emailPlaceholder")}
                  className="h-12"
                  required
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-foreground mb-2 block text-sm font-medium">
                  {t("contact.form.phone")}
                </label>
                <Input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder={t("contact.form.phonePlaceholder")}
                  className="h-12"
                />
              </div>
              <div>
                <label className="text-foreground mb-2 block text-sm font-medium">
                  {t("contact.form.subject")}
                </label>
                <Input
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  placeholder={t("contact.form.subjectPlaceholder")}
                  className="h-12"
                  required
                />
              </div>
            </div>

            <div>
              <label className="text-foreground mb-2 block text-sm font-medium">
                {t("contact.form.message")}
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                placeholder={t("contact.form.messagePlaceholder")}
                rows={6}
                className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex w-full resize-none rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                required
              />
            </div>

            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-gradient-ocean shadow-soft hover:shadow-elevated h-12 w-full font-semibold text-white transition-all duration-300 hover:scale-105 hover:opacity-90"
            >
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                  {t("contact.form.sending")}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  {t("contact.form.submit")}
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactForm;
