import { createTranslator } from "next-intl";
import { z } from "zod";

// Create a function that returns validation schemas with translated messages
export function createAuthValidations(t: ReturnType<typeof createTranslator>) {
  const signinSchema = z.object({
    email: z
      .string()
      .min(1, t("validations.required"))
      .email(t("validations.email")),
    password: z.string().min(6, t("validations.password.min", { min: 6 })),
  });

  const signupSchema = z
    .object({
      firstName: z.string().min(2, t("validations.minLength", { min: 2 })),
      lastName: z.string().min(2, t("validations.minLength", { min: 2 })),
      email: z
        .string()
        .min(1, t("validations.required"))
        .email(t("validations.email")),
      phoneNumber: z.string().min(10, t("validations.phoneNumber")),
      password: z
        .string()
        .min(6, t("validations.password.min", { min: 6 }))
        .regex(/[A-Z]/, t("validations.password.uppercase"))
        .regex(/[0-9]/, t("validations.password.number"))
        .regex(/[^A-Za-z0-9]/, t("validations.password.special")),
      confirmPassword: z.string().min(1, t("validations.required")),
    })
    .refine((data) => data.password === data.confirmPassword, {
      message: t("validations.password.match"),
      path: ["confirmPassword"],
    });

  return { signinSchema, signupSchema };
}
