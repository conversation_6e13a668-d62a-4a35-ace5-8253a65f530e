"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getUsers } from "../sercvices/users";
import { useEffect, useState } from "react";
import { User } from "../types/users";

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);

  useEffect(() => {
    getUsers({ pageNumber: 1, pageSize: 15 }).then((result) => {
      if (result.success) {
        setUsers(result.data.items);
      }
    });
  }, []);

  // if (!result.success) {
  //   return (
  //     <Alert className="mx-auto my-4 w-full max-w-lg" variant="destructive">
  //       <AlertTitle>خطأ</AlertTitle>
  //       <AlertDescription>{result.error}</AlertDescription>
  //     </Alert>
  //   );
  // }

  return (
    <div>
      UsersPage
      <pre dir="ltr" className="whitespace-pre-wrap">
        {JSON.stringify(users, null, 2)}
      </pre>
    </div>
  );
}
