import { Users, Building, Handshake } from "lucide-react";
import { useTranslations } from "next-intl";

interface PartnerType {
  icon: React.ElementType;
  title: string;
  description: string;
}

const PartnerTypesSection: React.FC = () => {
  const t = useTranslations("realEstate.partnerTypes");

  const partnerTypes: PartnerType[] = [
    {
      icon: Users,
      title: t("propertyOwners.title"),
      description: t("propertyOwners.description"),
    },
    {
      icon: Building,
      title: t("developers.title"),
      description: t("developers.description"),
    },
    {
      icon: Handshake,
      title: t("agents.title"),
      description: t("agents.description"),
    },
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold text-gray-800 md:text-4xl">
            {t("title")}
          </h2>
          <p className="mt-2 text-lg text-gray-600">{t("description")}</p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {partnerTypes.map((partner, index) => (
            <div
              key={index}
              className="rounded-lg bg-white p-8 text-center shadow-md"
            >
              <partner.icon className="text-primary mx-auto mb-4 h-12 w-12" />
              <h3 className="mb-2 text-xl font-semibold text-gray-800">
                {partner.title}
              </h3>
              <p className="text-gray-600">{partner.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PartnerTypesSection;
