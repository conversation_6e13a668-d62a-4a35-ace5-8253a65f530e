import { useMutation, useQueryClient } from "@tanstack/react-query";
import { loginApi } from "@/api/auth";
import { useAuthStore } from "@/store/authState";

export const useLogin = () => {
  const queryClient = useQueryClient();
  const { setAuth } = useAuthStore();

  return useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) =>
      loginApi(email, password),
    onSuccess: (data) => {
      if (data && data.token) {
        setAuth(data.user, data.token);
      }
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
};
