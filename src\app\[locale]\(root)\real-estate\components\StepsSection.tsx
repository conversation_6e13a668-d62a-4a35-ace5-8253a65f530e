import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Rocket } from "lucide-react";
import { useTranslations } from "next-intl";

interface Step {
  icon: React.ElementType;
  title: string;
  description: string;
}

const StepsSection: React.FC = () => {
  const t = useTranslations("realEstate.steps");

  const steps: Step[] = [
    {
      icon: FileText,
      title: t("submit.title"),
      description: t("submit.description"),
    },
    {
      icon: Clipboard<PERSON>heck,
      title: t("review.title"),
      description: t("review.description"),
    },
    {
      icon: Award,
      title: t("onboarding.title"),
      description: t("onboarding.description"),
    },
    {
      icon: Rocket,
      title: t("launch.title"),
      description: t("launch.description"),
    },
  ];

  return (
    <section className="bg-gray-50 py-20">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold text-gray-800 md:text-4xl">
            {t("title")}
          </h2>
          <p className="mt-2 text-lg text-gray-600">{t("description")}</p>
        </div>
        <div className="relative">
          <div className="absolute top-1/2 left-0 hidden h-0.5 w-full bg-gray-300 md:block"></div>
          <div className="relative grid grid-cols-1 gap-8 md:grid-cols-4">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="relative inline-block">
                  <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full border-2 border-gray-300 bg-white">
                    <step.icon className="text-primary h-8 w-8" />
                  </div>
                </div>
                <h3 className="mt-4 mb-2 text-xl font-semibold text-gray-800">
                  {step.title}
                </h3>
                <p className="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default StepsSection;
