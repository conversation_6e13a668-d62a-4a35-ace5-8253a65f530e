"use client";
import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Upload, Send } from "lucide-react";
import { useState } from "react";

interface ApplicationFormState {
  name: string;
  email: string;
  phone: string;
  linkedin: string;
  message: string;
  resume: File | null;
}

const ApplicationFormSection: React.FC = () => {
  const t = useTranslations();
  const [applicationForm, setApplicationForm] = useState<ApplicationFormState>({
    name: "",
    email: "",
    phone: "",
    linkedin: "",
    message: "",
    resume: null,
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setApplicationForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setApplicationForm((prev) => ({ ...prev, resume: e.target.files[0] }));
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle form submission logic
    console.log(applicationForm);
  };

  return (
    <section className="from-background to-brand-warm/30 bg-gradient-to-b py-20 lg:py-32">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <Badge className="bg-primary/10 text-primary border-primary/20 mb-4">
            {t("careers.application.badge")}
          </Badge>
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-5xl">
            {t("careers.application.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
            {t("careers.application.subtitle")}
          </p>
        </div>

        <Card className="shadow-elevated bg-background overflow-hidden border-0">
          <div className="from-primary/5 to-brand-ocean/5 absolute inset-0 bg-gradient-to-br"></div>
          <CardContent className="relative z-10 p-8 lg:p-12">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <label className="text-foreground mb-2 block text-sm font-medium">
                    {t("careers.application.form.name")}
                  </label>
                  <Input
                    type="text"
                    name="name"
                    value={applicationForm.name}
                    onChange={handleInputChange}
                    placeholder={t("careers.application.form.namePlaceholder")}
                    className="w-full"
                    required
                  />
                </div>
                <div>
                  <label className="text-foreground mb-2 block text-sm font-medium">
                    {t("careers.application.form.email")}
                  </label>
                  <Input
                    type="email"
                    name="email"
                    value={applicationForm.email}
                    onChange={handleInputChange}
                    placeholder={t("careers.application.form.emailPlaceholder")}
                    className="w-full"
                    required
                  />
                </div>
                <div>
                  <label className="text-foreground mb-2 block text-sm font-medium">
                    {t("careers.application.form.phone")}
                  </label>
                  <Input
                    type="tel"
                    name="phone"
                    value={applicationForm.phone}
                    onChange={handleInputChange}
                    placeholder={t("careers.application.form.phonePlaceholder")}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="text-foreground mb-2 block text-sm font-medium">
                    {t("careers.application.form.linkedin")}
                  </label>
                  <Input
                    type="url"
                    name="linkedin"
                    value={applicationForm.linkedin}
                    onChange={handleInputChange}
                    placeholder={t(
                      "careers.application.form.linkedinPlaceholder",
                    )}
                    className="w-full"
                  />
                </div>
              </div>
              <div>
                <label className="text-foreground mb-2 block text-sm font-medium">
                  {t("careers.application.form.message")}
                </label>
                <Textarea
                  name="message"
                  value={applicationForm.message}
                  onChange={handleInputChange}
                  placeholder={t("careers.application.form.messagePlaceholder")}
                  className="w-full"
                  rows={5}
                />
              </div>
              <div>
                <label className="text-foreground mb-2 block text-sm font-medium">
                  {t("careers.application.form.resume")}
                </label>
                <div className="relative flex w-full items-center justify-center">
                  <label className="bg-background hover:bg-brand-warm/10 flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed transition-colors duration-300">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Upload className="text-muted-foreground mb-3 h-8 w-8" />
                      <p className="text-muted-foreground mb-2 text-sm">
                        <span className="font-semibold">
                          {t("careers.application.form.uploadClick")}
                        </span>
                        {t("careers.application.form.uploadOrDrag")}
                      </p>
                      <p className="text-muted-foreground text-xs">
                        {t("careers.application.form.uploadHint")}
                      </p>
                    </div>
                    <input
                      type="file"
                      name="resume"
                      onChange={handleFileChange}
                      className="hidden"
                      accept=".pdf,.doc,.docx"
                    />
                  </label>
                </div>
                {applicationForm.resume && (
                  <p className="text-muted-foreground mt-2 text-sm">
                    {t("careers.application.form.fileSelected")}
                    {applicationForm.resume.name}
                  </p>
                )}
              </div>
              <div className="text-center">
                <Button
                  type="submit"
                  size="lg"
                  className="bg-primary hover:bg-primary/90 w-full text-white transition-all duration-300 md:w-auto"
                >
                  {t("careers.application.form.submit")}
                  <Send className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default ApplicationFormSection;
