import React, { Suspense, useState, useEffect, ReactNode } from "react";
import { useIntersectionObserver } from "@/hooks/useIntersectionObserver";

const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center py-20">
    <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-[#279fc7]"></div>
  </div>
);

interface LazySectionProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  animationType?: "fade-up" | "fade-in" | "slide-left" | "slide-right";
}

const LazySection: React.FC<LazySectionProps> = ({
  children,
  fallback = <LoadingSpinner />,
  className = "",
  threshold = 0.1,
  rootMargin = "100px",
  animationType = "fade-up",
}) => {
  const { elementRef, hasIntersected } =
    useIntersectionObserver<HTMLDivElement>({
      threshold,
      rootMargin,
    });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (hasIntersected) {
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    }
  }, [hasIntersected]);

  const getAnimationClasses = () => {
    const baseClasses = "transition-all duration-700 ease-out";

    if (!hasIntersected || !isVisible) {
      switch (animationType) {
        case "fade-up":
          return `${baseClasses} opacity-0 translate-y-8`;
        case "fade-in":
          return `${baseClasses} opacity-0`;
        case "slide-left":
          return `${baseClasses} opacity-0 -translate-x-8`;
        case "slide-right":
          return `${baseClasses} opacity-0 translate-x-8`;
        default:
          return `${baseClasses} opacity-0 translate-y-8`;
      }
    }

    return `${baseClasses} opacity-100 translate-y-0 translate-x-0`;
  };

  return (
    <div ref={elementRef} className={className}>
      {hasIntersected ? (
        <div className={getAnimationClasses()}>
          <Suspense fallback={fallback}>{children}</Suspense>
        </div>
      ) : (
        <div className="flex min-h-[200px] items-center justify-center opacity-0">
          <div className="bg-muted/20 h-32 w-full animate-pulse rounded-lg"></div>
        </div>
      )}
    </div>
  );
};

export default LazySection;
