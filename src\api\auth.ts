import api from "@/lib/axios";
import { LoginResponse, RegisterRequest } from "@/types/auth";

const BASE_ROUTE = "/Auth";
export const loginApi = async (email: string, password: string) => {
  const { data } = await api.post<LoginResponse>(BASE_ROUTE, {
    email,
    password,
  });
  localStorage.setItem("token", data.token);
  localStorage.setItem("refreshToken", data.refreshToken);
  return data;
};

export const registerApi = async (request: RegisterRequest) => {
  const data = await api.post<LoginResponse>(`${BASE_ROUTE}/register`, request);
  return data;
};

export const externalLoginApi = async (provider: string) => {
  const data = await api.post(`${BASE_ROUTE}/externallogin`, provider);
  return data;
};

export const externalRegisterApi = async (provider: string) => {
  const data = await api.post<LoginResponse>(`${BASE_ROUTE}/signin`, provider);
  return data;
};
