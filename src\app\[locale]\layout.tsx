import InitiateAuth from "@/components/auth/InitiateAuth";
import { routing } from "@/i18n/routing";
import { LanguageProvider } from "@/providers/LanguageContext";
import { ThemeProvider } from "@/providers/ThemeProvider";
import { Metadata } from "next";
import { Locale, NextIntlClientProvider, hasLocale } from "next-intl";
import { getTranslations, setRequestLocale } from "next-intl/server";
import { Almarai } from "next/font/google";
import { notFound } from "next/navigation";
import QueryClientProvider from "@/providers/QueryClientProvider";

const almarai = Almarai({
  subsets: ["arabic", "latin"],
  display: "swap",
  weight: ["300", "400", "700", "800"],
  variable: "--font-almarai",
});

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "metadata" });

  return {
    title: t("title"),
  };
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <html
      dir={locale === "ar" ? "rtl" : "ltr"}
      lang={locale}
      suppressHydrationWarning
      className={`scroll-smooth ${almarai.variable}`}
    >
      <body>
        <QueryClientProvider>
          <InitiateAuth />
          <NextIntlClientProvider locale={locale}>
            <ThemeProvider>
              <LanguageProvider>{children}</LanguageProvider>
            </ThemeProvider>
          </NextIntlClientProvider>
        </QueryClientProvider>
      </body>
    </html>
  );
}
