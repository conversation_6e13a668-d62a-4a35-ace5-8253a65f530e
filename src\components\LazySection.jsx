import React, { Suspense, useState, useEffect } from 'react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-20">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#279fc7]"></div>
  </div>
);

const LazySection = ({ 
  children, 
  fallback = <LoadingSpinner />, 
  className = "",
  threshold = 0.1,
  rootMargin = "100px",
  animationType = "fade-up" // "fade-up", "fade-in", "slide-left", "slide-right"
}) => {
  const { elementRef, hasIntersected } = useIntersectionObserver({
    threshold,
    rootMargin
  });
  
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    if (hasIntersected) {
      // Small delay to ensure smooth animation
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    }
  }, [hasIntersected]);
  
  const getAnimationClasses = () => {
    const baseClasses = "transition-all duration-700 ease-out";
    
    if (!hasIntersected || !isVisible) {
      switch (animationType) {
        case "fade-up":
          return `${baseClasses} opacity-0 translate-y-8`;
        case "fade-in":
          return `${baseClasses} opacity-0`;
        case "slide-left":
          return `${baseClasses} opacity-0 -translate-x-8`;
        case "slide-right":
          return `${baseClasses} opacity-0 translate-x-8`;
        default:
          return `${baseClasses} opacity-0 translate-y-8`;
      }
    }
    
    return `${baseClasses} opacity-100 translate-y-0 translate-x-0`;
  };

  return (
    <div ref={elementRef} className={className}>
      {hasIntersected ? (
        <div className={getAnimationClasses()}>
          <Suspense fallback={fallback}>
            {children}
          </Suspense>
        </div>
      ) : (
        <div className="min-h-[200px] flex items-center justify-center opacity-0">
          <div className="w-full h-32 bg-muted/20 animate-pulse rounded-lg"></div>
        </div>
      )}
    </div>
  );
};

export default LazySection;