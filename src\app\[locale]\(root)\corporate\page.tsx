import React from "react";
import {
  AccommodationTypes,
  AmenitiesSection,
  BenefitsSection,
  CTASection,
  HeroSection,
  TrustedBySection,
  ValueProposition,
} from "./components";
import ScrollRevealSection from "@/components/ScrollReveal";

const CorporateStay: React.FC = () => {
  return (
    <div className="bg-background text-foreground min-h-screen">
      {/* Hero Section - Always visible */}
      <ScrollRevealSection>
        <HeroSection />
      </ScrollRevealSection>

      {/* Value Proposition Section */}
      <ScrollRevealSection>
        <ValueProposition />
      </ScrollRevealSection>

      {/* Accommodation Types Section */}
      <ScrollRevealSection>
        <AccommodationTypes />
      </ScrollRevealSection>

      {/* Benefits Section */}
      <ScrollRevealSection>
        <BenefitsSection />
      </ScrollRevealSection>

      {/* Amenities Section */}
      <ScrollRevealSection>
        <AmenitiesSection />
      </ScrollRevealSection>

      {/* Trusted By Section */}
      <TrustedBySection />

      {/* CTA Section */}
      <ScrollRevealSection>
        <CTASection />
      </ScrollRevealSection>
    </div>
  );
};

export default CorporateStay;
