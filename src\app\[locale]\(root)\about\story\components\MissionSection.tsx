import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, Award, CheckCircle } from "lucide-react";

const MissionSection: React.FC = () => {
  const t = useTranslations();

  return (
    <section className="bg-background py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          <div>
            <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
              <Heart className="text-primary mr-2 h-4 w-4" />
              <span className="text-primary text-sm font-semibold">
                {t("about.mission.subtitle")}
              </span>
            </div>
            <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
              {t("about.mission.title")}
            </h2>
            <p className="text-muted-foreground mb-8 text-lg leading-relaxed">
              {t("about.mission.description")}
            </p>
            <div className="space-y-4">
              {[
                t("about.mission.points.luxury"),
                t("about.mission.points.technology"),
                t("about.mission.points.service"),
              ].map((point, index) => (
                <div key={index} className="flex items-start">
                  <CheckCircle className="text-primary mt-1 mr-3 h-5 w-5 flex-shrink-0" />
                  <span className="text-muted-foreground">{point}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <Card className="shadow-elevated from-primary/5 to-brand-ocean/10 border-0 bg-gradient-to-br">
              <CardContent className="p-8">
                <Award className="text-primary mb-6 h-12 w-12" />
                <h3 className="text-foreground mb-4 text-xl font-bold">
                  {t("about.mission.award.title")}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {t("about.mission.award.description")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MissionSection;
