import logoImage from "@/../public/logo.png";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { useAuthDialogStore } from "@/store/authDialogStore";
import { useTranslations } from "next-intl";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "../ui/dialog";
import SigninForm from "./SigninForm";
import SignupForm from "./SignupForm";

const AuthDialog = () => {
  const t = useTranslations();

  const isOpen = useAuthDialogStore((state) => state.isOpen);
  const authType = useAuthDialogStore((state) => state.authType);
  const setAuthType = useAuthDialogStore((state) => state.setAuthType);
  const closeDialog = useAuthDialogStore((state) => state.closeDialog);
  const message = useAuthDialogStore((state) => state.message);
  // const setAuthDialogMessage = useAuthDialogStore((state) => state.setMessage);

  const activeTab = authType;

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => (!open ? closeDialog() : undefined)}
    >
      <DialogContent className="border-0 p-0 shadow-2xl">
        {/* Header */}

        <div className="flex justify-center pt-8 pb-5">
          <Image
            src={logoImage}
            alt="Hala Logo"
            className="h-12 w-12 object-contain"
          />
        </div>

        <DialogTitle asChild>
          <h2 className="mb-4 text-center text-xl font-bold">
            {authType === "signin" ? t("auth.signIn") : t("auth.signUp")}
          </h2>
        </DialogTitle>

        <DialogDescription className="sr-only">
          {authType === "signin" ? t("auth.signIn") : t("auth.signUp")}
        </DialogDescription>

        {/* Content */}
        <div className="px-6 pb-6">
          {message && (
            <div
              className={cn("my-4 rounded-2xl border p-4", {
                "border-green-200 bg-green-100 text-green-600":
                  message.type === "success",
                "border-red-300 bg-red-100 text-red-600":
                  message.type === "error",
              })}
            >
              <p className="text-lg">{message.text}</p>
            </div>
          )}

          {/* Tab Buttons */}
          <div className="mb-6 flex rounded-lg bg-gray-100 p-1 dark:bg-gray-800">
            <button
              onClick={() => setAuthType("signin")}
              className={`flex-1 rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                authType === "signin"
                  ? `dark:bg-gray-70 bg-white text-gray-900 shadow-sm dark:text-gray-900 dark:shadow-none`
                  : `text-gray-600 hover:text-gray-900 dark:text-white/80`
              }`}
            >
              {t("auth.signIn")}
            </button>
            <button
              onClick={() => setAuthType("signup")}
              className={`flex-1 rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                authType === "signup"
                  ? `dark:bg-gray-70 bg-white text-gray-900 shadow-sm dark:text-gray-900 dark:shadow-none`
                  : `d text-gray-600 hover:text-gray-900 dark:text-white/80`
              }`}
            >
              {t("auth.signUp")}
            </button>
          </div>

          {/* Login Form */}
          {activeTab === "signin" && <SigninForm />}

          {/* Register Form */}
          {activeTab === "signup" && <SignupForm />}

          {/* Separator */}
          <div className="my-6">
            <Separator />
            <div className="mt-4 text-center">
              <span className="text-sm">{t("auth.byContinuing")} </span>
              <Button
                variant="link"
                className="h-auto p-0 text-sm text-[#279fc7] hover:text-[#279fc7]/90"
              >
                {t("auth.termsOfService")}
              </Button>{" "}
              <span className="text-sm">{t("auth.and")} </span>
              <Button
                variant="link"
                className="h-auto p-0 text-sm text-[#279fc7] hover:text-[#279fc7]/90"
              >
                {t("auth.privacyPolicy")}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
