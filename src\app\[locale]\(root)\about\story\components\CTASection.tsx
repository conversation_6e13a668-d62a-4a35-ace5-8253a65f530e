import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/i18n/navigation";
import { ArrowRight } from "lucide-react";

const CTASection: React.FC = () => {
  const t = useTranslations();

  return (
    <section className="bg-background py-20">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="from-primary to-brand-ocean shadow-elevated rounded-2xl bg-gradient-to-br p-12 text-center">
          <h2 className="mb-4 text-3xl font-bold text-white md:text-4xl">
            {t("about.cta.title")}
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-lg text-white/90">
            {t("about.cta.description")}
          </p>
          <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
            <Link href="/contact">
              <Button
                size="lg"
                variant="secondary"
                className="text-primary w-full bg-white transition-transform duration-300 hover:scale-105"
              >
                Contact Us
              </Button>
            </Link>
            <Link href="/real-estate">
              <Button
                size="lg"
                variant="outline"
                className="w-full border-white text-white transition-transform duration-300 hover:scale-105 hover:bg-white/10"
              >
                See Properties
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
