"use client";
import LazySection from "@/components/LazySection";
import ContactHero from "@/app/[locale]/(root)/contact/components/ContactHero";
import ContactMethods from "@/app/[locale]/(root)/contact/components/ContactMethods";
import ContactForm from "@/app/[locale]/(root)/contact/components/ContactForm";
import SupportFeatures from "@/app/[locale]/(root)/contact/components/SupportFeatures";

const ContactUs: React.FC = () => {
  return (
    <div className="bg-background min-h-screen">
      <main>
        <LazySection animationType="fade-up" threshold={0.2} rootMargin="0px">
          <ContactHero />
        </LazySection>

        <LazySection
          animationType="slide-left"
          threshold={0.2}
          rootMargin="0px"
        >
          <ContactMethods />
        </LazySection>

        <section className="from-brand-warm/30 via-background to-primary/5 bg-gradient-to-br py-24">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="grid items-start gap-16 lg:grid-cols-2">
              <LazySection
                animationType="slide-right"
                threshold={0.2}
                rootMargin="0px"
              >
                <ContactForm />
              </LazySection>

              <LazySection
                animationType="fade-in"
                threshold={0.2}
                rootMargin="0px"
              >
                <SupportFeatures />
              </LazySection>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default ContactUs;
