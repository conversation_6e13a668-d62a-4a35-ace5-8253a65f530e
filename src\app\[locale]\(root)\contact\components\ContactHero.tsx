"use client";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, Clock } from "lucide-react";

const ContactHero: React.FC = () => {
  const t = useTranslations();

  return (
    <section className="from-primary/10 via-background to-brand-warm/20 relative overflow-hidden bg-gradient-to-br py-24">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="bg-primary absolute top-20 left-10 h-32 w-32 rounded-full blur-3xl" />
        <div className="bg-brand-ocean absolute right-10 bottom-20 h-40 w-40 rounded-full blur-3xl" />
        <div className="bg-brand-warm absolute top-1/2 left-1/2 h-96 w-96 -translate-x-1/2 -translate-y-1/2 transform rounded-full blur-3xl" />
      </div>

      <div className="relative mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
        <Badge
          variant="outline"
          className="border-primary/20 text-primary bg-primary/5 hover:bg-primary/10 mb-6 px-4 py-2 text-sm font-medium transition-colors duration-300"
        >
          {t("contact.badge")}
        </Badge>

        <h1 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-6xl">
          <span className="from-primary via-brand-ocean to-brand-warm bg-gradient-to-r bg-clip-text text-transparent">
            {t("contact.title")}
          </span>
        </h1>

        <p className="text-muted-foreground mx-auto mb-12 max-w-3xl text-xl leading-relaxed">
          {t("contact.description")}
        </p>

        {/* Quick Contact Info */}
        <div className="flex flex-wrap justify-center gap-8 text-sm">
          <div className="text-muted-foreground flex items-center space-x-2">
            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
              <Mail className="text-primary h-4 w-4" />
            </div>
            <span><EMAIL></span>
          </div>
          <div className="text-muted-foreground flex items-center space-x-2">
            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
              <Phone className="text-primary h-4 w-4" />
            </div>
            <span>+****************</span>
          </div>
          <div className="text-muted-foreground flex items-center space-x-2">
            <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
              <Clock className="text-primary h-4 w-4" />
            </div>
            <span>24/7 Support</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactHero;
