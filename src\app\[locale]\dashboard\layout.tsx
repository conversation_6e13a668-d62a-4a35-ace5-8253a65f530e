import { ThemeSwitcher } from "@/components/header/ThemeSwitcher";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { AppSidebar } from "./components/app-sidebar";
import RouteGuard from "./RouteGuard";

import { redirect } from "@/i18n/navigation";
import { Locale } from "next-intl";
import "./globals.css";

export default async function DashboardLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}) {
  const locale = (await params).locale;
  console.log(locale);
  if (locale !== "ar") {
    redirect({ href: "/dashboard", locale: "ar", forcePrefix: true });
  }

  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body>
        <RouteGuard>
          <SidebarProvider>
            <AppSidebar variant="floating" side="right" />
            <SidebarInset>
              <header className="flex h-16 shrink-0 items-center gap-2">
                <div className="flex items-center gap-2 px-4">
                  <SidebarTrigger className="-ml-1" />
                  <Separator
                    orientation="vertical"
                    className="mr-2 data-[orientation=vertical]:h-4"
                  />
                  <ThemeSwitcher size="icon" />
                </div>
              </header>
              <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                {children}
              </div>
            </SidebarInset>
          </SidebarProvider>
        </RouteGuard>
      </body>
    </html>
  );
}
