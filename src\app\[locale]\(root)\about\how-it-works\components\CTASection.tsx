import { useTranslations } from "next-intl";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/i18n/navigation";

const CTASection: React.FC = () => {
  const t = useTranslations("howItWorks.cta");

  return (
    <section className="from-primary to-brand-ocean bg-gradient-to-r py-20 lg:py-32">
      <div className="mx-auto max-w-4xl px-4 text-center sm:px-6 lg:px-8">
        <h2 className="mb-6 text-3xl font-bold text-white md:text-4xl">
          {t("title")}
        </h2>
        <p className="mb-8 text-xl text-white/80">{t("description")}</p>
        <Link href="/about/careers">
          <Button
            size="lg"
            variant="secondary"
            className="text-primary rounded-full bg-white px-8 py-6 text-lg font-bold shadow-lg transition-transform duration-300 hover:scale-105 hover:bg-gray-100"
          >
            {t("button")}
          </Button>
        </Link>
      </div>
    </section>
  );
};

export default CTASection;
