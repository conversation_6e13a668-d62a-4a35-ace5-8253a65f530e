import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Building2 } from "lucide-react";
import { useTranslations } from "next-intl";

interface HeroSectionProps {}

const HeroSection: React.FC<HeroSectionProps> = () => {
  const t = useTranslations("realEstate");

  return (
    <section
      className="relative flex h-[80vh] items-center justify-center overflow-hidden text-center text-white"
      style={{
        backgroundImage:
          "url(https://stellastays.com/static/media/lease-banner.608ec187.webp)",
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-transparent" />
      <div className="animate-in fade-in relative z-10 max-w-4xl px-4 duration-1000">
        <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-white/20 bg-white/10 px-4 py-2 backdrop-blur-sm">
          <Building2 className="h-4 w-4" />
          <span className="text-sm font-medium">
            Premium Real Estate Partnership
          </span>
        </div>
        <h1 className="mb-6 bg-gradient-to-r from-white to-white/80 bg-clip-text text-4xl leading-24 font-bold text-transparent md:text-6xl lg:text-7xl">
          {t("hero.title")}
        </h1>
        <p className="mx-auto mb-8 max-w-2xl text-xl leading-relaxed text-white/90 md:text-2xl">
          {t("hero.description")}
        </p>
        <div className="flex flex-col items-center justify-center gap-6 sm:flex-row">
          <Button
            size="lg"
            className="bg-primary hover:bg-primary/95 group hover:shadow-3xl hover:border-primary/30 relative h-auto cursor-pointer overflow-hidden rounded-xl border-2 border-transparent px-10 py-5 text-lg font-semibold shadow-2xl transition-all duration-300 hover:scale-105"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/5 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            <span className="relative z-10">{t("hero.cta")}</span>
            <ArrowRight className="relative z-10 ml-3 h-5 w-5 transition-all duration-300 group-hover:translate-x-2 group-hover:scale-110" />
            <div className="from-primary-foreground/5 absolute inset-0 rounded-xl bg-gradient-to-r to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          </Button>
        </div>
      </div>
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 h-20 w-20 animate-pulse rounded-full bg-white/5 blur-xl" />
      <div className="bg-primary/20 absolute right-10 bottom-20 h-32 w-32 animate-pulse rounded-full blur-2xl delay-1000" />
    </section>
  );
};

export default HeroSection;
