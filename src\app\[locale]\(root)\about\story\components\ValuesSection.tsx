import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, Shield, Globe, Users, Sparkles } from "lucide-react";

interface Value {
  icon: React.ElementType;
  title: string;
  description: string;
}

const ValuesSection: React.FC = () => {
  const t = useTranslations();

  const values: Value[] = [
    {
      icon: Heart,
      title: t("about.values.excellence.title"),
      description: t("about.values.excellence.description"),
    },
    {
      icon: Shield,
      title: t("about.values.trust.title"),
      description: t("about.values.trust.description"),
    },
    {
      icon: Globe,
      title: t("about.values.innovation.title"),
      description: t("about.values.innovation.description"),
    },
    {
      icon: Users,
      title: t("about.values.community.title"),
      description: t("about.values.community.description"),
    },
  ];

  return (
    <section className="from-brand-warm/20 to-background bg-gradient-to-br py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
            <Sparkles className="text-primary mr-2 h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              {t("about.values.subtitle")}
            </span>
          </div>
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("about.values.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
            {t("about.values.description")}
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {values.map((value, index) => (
            <Card
              key={index}
              className="shadow-soft from-background to-brand-warm/10 hover:shadow-elevated border-0 bg-gradient-to-br text-center transition-all duration-300"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-8">
                <div className="bg-primary/10 text-primary mb-6 inline-flex h-16 w-16 items-center justify-center rounded-full">
                  <value.icon className="h-8 w-8" />
                </div>
                <h3 className="text-foreground mb-4 text-xl font-bold">
                  {value.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {value.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValuesSection;
