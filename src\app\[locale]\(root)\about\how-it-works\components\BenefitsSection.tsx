import { useTranslations } from "next-intl";
import { Card } from "@/components/ui/card";
import { Shield, Smartphone, MapPin } from "lucide-react";

interface Benefit {
  icon: React.ElementType;
  key: string;
  color: string;
}

const BenefitsSection: React.FC = () => {
  const t = useTranslations("howItWorks.benefits");

  const benefits: Benefit[] = [
    {
      icon: Shield,
      key: "verified",
      color: "from-primary to-brand-ocean",
    },
    {
      icon: Smartphone,
      key: "support",
      color: "from-brand-ocean to-primary",
    },
    {
      icon: Smartphone,
      key: "technology",
      color: "from-primary to-brand-teal-dark",
    },
    {
      icon: MapPin,
      key: "locations",
      color: "from-brand-teal-dark to-primary",
    },
  ];

  return (
    <section className="from-brand-warm/30 to-background bg-gradient-to-b py-20 lg:py-32">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
            {t("title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
            {t("subtitle")}
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {benefits.map((benefit, index) => (
            <Card
              key={benefit.key}
              className="shadow-soft bg-background hover:shadow-elevated group border-0 transition-all duration-300 hover:scale-105"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <div className="p-8 text-center">
                <div
                  className={`inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br ${benefit.color} shadow-soft mb-6 transition-transform duration-300 group-hover:scale-110`}
                >
                  <benefit.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-foreground mb-2 text-xl font-bold">
                  {t(`items.${benefit.key}.title`)}
                </h3>
                <p className="text-muted-foreground">
                  {t(`items.${benefit.key}.description`)}
                </p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
