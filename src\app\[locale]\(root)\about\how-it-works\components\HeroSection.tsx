import { useTranslations } from "next-intl";
import { CheckCircle } from "lucide-react";

const HeroSection: React.FC = () => {
  const t = useTranslations("howItWorks");

  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      <div className="from-brand-warm via-background to-brand-teal-light/10 absolute inset-0 bg-gradient-to-br"></div>
      <div className="absolute inset-0 opacity-40">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23279fc7' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="relative mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
            <CheckCircle className="text-primary mr-2 h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              {t("badge")}
            </span>
          </div>
          <h1 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
            {t("title")}
          </h1>
          <p className="text-muted-foreground mx-auto mb-4 max-w-3xl text-xl leading-relaxed">
            {t("subtitle")}
          </p>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed">
            {t("description")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
