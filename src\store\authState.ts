import { create } from "zustand";
import { persist } from "zustand/middleware";
import { jwtDecode } from "jwt-decode";
import { User } from "@/types/auth";

interface AuthState {
  user: User | null;
  role: string | null;
  setAuth: (user: User, token: string) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      role: null,

      setAuth: (user, token) => {
        const decoded: any = jwtDecode(token);

        // check expiry
        if (decoded.exp * 1000 < Date.now()) {
          localStorage.removeItem("token");
          set({ user: null, role: null });
          return;
        }

        set({
          user,
          role: decoded.roles?.[0] || null,
        });
      },

      logout: () => {
        set({ user: null, role: null });
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({ user: state.user, role: state.role }),
    },
  ),
);
