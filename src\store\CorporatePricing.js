import { create } from "zustand";
import axios from "axios";

const useCorporatePricingStore = create((set) => ({
  requests: [],
  currentRequest: null,
  isLoading: false,
  error: null,

  // Fetch all corporate pricing requests
  fetchRequests: async () => {
    set({ isLoading: true });
    try {
      const response = await axios.get("/api/corporate-pricing-requests");
      set({ requests: response.data, isLoading: false });
    } catch (error) {
      set({ error: error.message, isLoading: false });
    }
  },

  // Fetch a single corporate pricing request by ID
  fetchRequestById: async (id) => {
    set({ isLoading: true });
    try {
      const response = await axios.get(`/api/corporate-pricing-requests/${id}`);
      set({ currentRequest: response.data, isLoading: false });
    } catch (error) {
      set({ error: error.message, isLoading: false });
    }
  },

  // Create a new corporate pricing request
  createRequest: async (requestData) => {
    set({ isLoading: true });
    try {
      const response = await axios.post(
        "/api/corporate-pricing-requests",
        requestData
      );
      set((state) => ({
        requests: [...state.requests, response.data],
        isLoading: false,
      }));
      return response.data;
    } catch (error) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Update request status (approve/reject)
  updateRequestStatus: async (id, status) => {
    set({ isLoading: true });
    try {
      const response = await axios.patch(
        `/api/corporate-pricing-requests/${id}`,
        { status }
      );
      set((state) => ({
        requests: state.requests.map((req) =>
          req.id === id ? { ...req, ...response.data } : req
        ),
        currentRequest:
          state.currentRequest?.id === id
            ? response.data
            : state.currentRequest,
        isLoading: false,
      }));
    } catch (error) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Delete a corporate pricing request
  deleteRequest: async (id) => {
    set({ isLoading: true });
    try {
      await axios.delete(`/api/corporate-pricing-requests/${id}`);
      set((state) => ({
        requests: state.requests.filter((req) => req.id !== id),
        currentRequest:
          state.currentRequest?.id === id ? null : state.currentRequest,
        isLoading: false,
      }));
    } catch (error) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },

  // Clear current request
  clearCurrentRequest: () => set({ currentRequest: null }),

  // Clear error
  clearError: () => set({ error: null }),
}));

export default useCorporatePricingStore;
