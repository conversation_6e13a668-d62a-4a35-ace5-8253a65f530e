import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Calendar, Star, CheckCircle } from "lucide-react";

interface Step {
  number: string;
  icon: React.ElementType;
  key: string;
  color: string;
}

const StepsSection: React.FC = () => {
  const t = useTranslations("howItWorks.steps");

  const steps: Step[] = [
    {
      number: "01",
      icon: Search,
      key: "search",
      color: "from-blue-500 to-cyan-500",
    },
    {
      number: "02",
      icon: Calendar,
      key: "book",
      color: "from-cyan-500 to-teal-500",
    },
    {
      number: "03",
      icon: Star,
      key: "experience",
      color: "from-teal-500 to-emerald-500",
    },
  ];

  return (
    <section className="from-background to-brand-warm/30 bg-gradient-to-b py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid gap-12 lg:grid-cols-3 lg:gap-8">
          {steps.map((step, index) => (
            <div
              key={step.key}
              className="group relative"
              style={{ animationDelay: `${index * 200}ms` }}
            >
              {index < steps.length - 1 && (
                <div className="from-primary/30 absolute top-20 left-full z-0 hidden h-0.5 w-full translate-x-4 transform bg-gradient-to-r to-transparent lg:block"></div>
              )}

              <Card className="shadow-soft bg-background hover:shadow-elevated relative overflow-hidden border-0 transition-all duration-500 group-hover:scale-105">
                <div className="from-primary/5 to-brand-ocean/5 absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>

                <CardContent className="relative z-10 p-8 text-center">
                  <div className="from-primary/10 to-brand-ocean/10 mb-6 inline-flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br transition-transform duration-300 group-hover:scale-110">
                    <span className="from-primary to-brand-ocean bg-gradient-to-r bg-clip-text text-2xl font-bold text-transparent">
                      {step.number}
                    </span>
                  </div>

                  <div
                    className={`inline-flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br ${step.color} shadow-soft mb-6 transition-transform duration-300 group-hover:scale-110`}
                  >
                    <step.icon className="h-10 w-10 text-white" />
                  </div>

                  <h3 className="text-foreground mb-4 text-2xl font-bold">
                    {t(`${step.key}.title`)}
                  </h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {t(`${step.key}.description`)}
                  </p>

                  <ul className="space-y-3 text-left">
                    {[0, 1, 2, 3]
                      .map((featureIndex) => {
                        const featureKey =
                          `${step.key}.features.${featureIndex}` as const;
                        const feature = t(featureKey, {
                          defaultValue: null,
                        });
                        if (!feature) return null;
                        return (
                          <li
                            key={featureIndex}
                            className="text-muted-foreground flex items-center text-sm"
                          >
                            <CheckCircle
                              className={`text-primary mr-2 h-4 w-4 flex-shrink-0`}
                            />
                            {feature}
                          </li>
                        );
                      })
                      .filter(Boolean)}
                  </ul>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StepsSection;
