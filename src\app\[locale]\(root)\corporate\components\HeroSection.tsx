import { Badge } from "@/components/ui/badge";
import { useTranslations } from "next-intl";
import { ContactForm } from "./ContactForm";

const HeroSection: React.FC = () => {
  const t = useTranslations("corporate");

  return (
    <section className="relative flex min-h-[80vh] items-center justify-center overflow-hidden">
      <div className="absolute inset-0">
        <img
          src="/CorporateStay/corporate_banner.webp"
          alt="Corporate Stay Hero"
          className="h-full w-full object-cover"
        />
        <div className="absolute inset-0 bg-black/50" />
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-4 text-center text-white sm:px-6 lg:px-8">
        <Badge className="mb-6 border-white/30 bg-white/20 text-white transition-all duration-300 hover:bg-white/30">
          ✨ {t("hero.subtitle")}
        </Badge>

        <h1 className="animate-fade-in mb-6 text-4xl leading-tight font-bold md:text-6xl lg:text-7xl">
          <span className="block">{t("hero.title")}</span>
        </h1>

        <p className="animate-fade-in-delay mx-auto mb-8 max-w-3xl text-xl leading-relaxed opacity-90 md:text-2xl">
          {t("hero.subtitle")}
        </p>
        <ContactForm />
      </div>
    </section>
  );
};

export default HeroSection;
