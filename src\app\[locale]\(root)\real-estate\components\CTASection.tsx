import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

const CTASection: React.FC = () => {
  const t = useTranslations("realEstate.cta");

  return (
    <section className="bg-primary py-20 text-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="mb-4 text-3xl font-bold md:text-4xl">{t("title")}</h2>
        <p className="mx-auto mb-8 max-w-2xl text-lg md:text-xl">
          {t("description")}
        </p>
        <Button
          size="lg"
          variant="secondary"
          className="text-primary bg-white hover:bg-gray-100"
        >
          {t("cta")}
        </Button>
      </div>
    </section>
  );
};

export default CTASection;
