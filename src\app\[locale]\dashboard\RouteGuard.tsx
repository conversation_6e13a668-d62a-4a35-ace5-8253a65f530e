"use client";

import { useAuthStore } from "@/store/auth";
import { redirect } from "next/navigation";
import React, { useEffect, useState } from "react";

export default function RouteGuard({
  children,
}: {
  children: React.ReactNode;
}) {
  const [shouldRender, setShouldRender] = useState(false);
  const [isClientSide, setIsClientSide] = useState(false);
  const isAdminFn = useAuthStore((state) => state.isAdmin);

  useEffect(() => {
    setIsClientSide(true);
    const isAdmin = isAdminFn();
    setShouldRender(isAdmin);
  }, []);

  if (!isClientSide) {
    return null;
  }

  if (!shouldRender) {
    return redirect("/");
  }

  return <>{children}</>;
}
