import { useTranslations } from "next-intl";

interface Milestone {
  year: string;
  title: string;
  description: string;
}

const MilestonesSection: React.FC = () => {
  const t = useTranslations();

  const milestones: Milestone[] = [
    {
      year: "2020",
      title: t("about.milestones.founded.title"),
      description: t("about.milestones.founded.description"),
    },
    {
      year: "2021",
      title: t("about.milestones.expansion.title"),
      description: t("about.milestones.expansion.description"),
    },
    {
      year: "2022",
      title: t("about.milestones.recognition.title"),
      description: t("about.milestones.recognition.description"),
    },
    {
      year: "2024",
      title: t("about.milestones.global.title"),
      description: t("about.milestones.global.description"),
    },
  ];

  return (
    <section className="bg-background py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("about.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
            {t("about.description")}
          </p>
        </div>
        <div className="relative">
          {/* Timeline line */}
          <div className="bg-border absolute top-0 left-1/2 h-full w-0.5 -translate-x-1/2"></div>

          <div className="space-y-12">
            {milestones.map((milestone, index) => (
              <div
                key={index}
                className={`relative flex items-center ${index % 2 === 0 ? "justify-start" : "justify-end"}`}
              >
                <div
                  className={`w-5/12 ${index % 2 === 0 ? "pr-8 text-right" : "pl-8 text-left"}`}
                >
                  <div className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-3 py-1 text-sm font-semibold">
                    {milestone.year}
                  </div>
                  <h3 className="text-foreground mb-2 text-xl font-bold">
                    {milestone.title}
                  </h3>
                  <p className="text-muted-foreground">
                    {milestone.description}
                  </p>
                </div>
                <div className="bg-primary absolute left-1/2 z-10 h-4 w-4 -translate-x-1/2 rounded-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default MilestonesSection;
