"use client";

import { Check, Play, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useLanguage } from "@/providers/LanguageContext";
import { useState } from "react";

const LuxuryExperience = () => {
  const t = useTranslations();
  const { isRTL } = useLanguage();
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const features = [
    "luxuryExperience.features.concierge",
    "luxuryExperience.features.amenities",
    "luxuryExperience.features.dining",
    "luxuryExperience.features.spa",
  ];

  return (
    <section className="bg-gradient-to-br from-slate-50 via-white to-blue-50 px-4 py-20 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <div className="space-y-8">
            <div>
              <h2 className="mb-4 bg-gradient-to-r from-slate-800 via-blue-900 to-slate-700 bg-clip-text text-4xl font-bold text-transparent md:text-5xl">
                {t("luxuryExperience.title")}
              </h2>
              <h3
                className="mb-6 text-2xl font-semibold md:text-3xl"
                style={{ color: "#279fc7" }}
              >
                {t("luxuryExperience.subtitle")}
              </h3>
              <p className="text-lg leading-relaxed text-slate-600">
                {t("luxuryExperience.description")}
              </p>
            </div>

            <div className="space-y-4">
              {features.map((featureKey, index) => (
                <div
                  key={index}
                  className={`flex items-center ${
                    isRTL ? "space-x-3 space-x-reverse" : "space-x-3"
                  }`}
                >
                  <div
                    className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full shadow-sm"
                    style={{ backgroundColor: "#279fc7" }}
                  >
                    <Check className="h-4 w-4 text-white" strokeWidth={2} />
                  </div>
                  <span className="font-medium text-slate-700">
                    {t(featureKey)}
                  </span>
                </div>
              ))}
            </div>

            <button
              onClick={() => setIsVideoModalOpen(true)}
              className={`group inline-flex transform items-center rounded-2xl bg-slate-800 px-6 py-3 text-white shadow-lg transition-all duration-300 hover:-translate-y-1 hover:bg-slate-700 hover:shadow-xl ${
                isRTL ? "space-x-3 space-x-reverse" : "space-x-3"
              }`}
            >
              <span className="text-sm font-medium">
                {t("luxuryExperience.watchVideo")}
              </span>
              <div className="ml-2 flex h-6 w-6 items-center justify-center rounded-full bg-white">
                <Play className="h-3 w-3 text-slate-800 transition-transform duration-300 group-hover:scale-110" />
              </div>
            </button>
          </div>

          <div className="relative">
            <div className="relative overflow-hidden rounded-2xl shadow-2xl">
              <img
                alt={t("luxuryExperience.imageAlt")}
                className="h-96 w-full object-cover lg:h-[500px]"
                src="https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                <button
                  onClick={() => setIsVideoModalOpen(true)}
                  className="group flex h-20 w-20 items-center justify-center rounded-full border border-white/20 bg-white/95 shadow-xl backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:bg-white"
                >
                  <Play className="ml-1 h-8 w-8 text-slate-700 transition-transform duration-300 group-hover:scale-110" />
                </button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-blue-400/20 blur-xl"></div>
            <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-slate-400/20 blur-xl"></div>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {isVideoModalOpen && (
        <div
          className="animate-in fade-in fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm duration-300"
          onClick={() => setIsVideoModalOpen(false)}
        >
          <div
            className="animate-in zoom-in-95 slide-in-from-bottom-4 relative mx-4 w-full max-w-4xl duration-300"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={() => setIsVideoModalOpen(false)}
              className="animate-in slide-in-from-top-2 absolute -top-12 right-0 flex h-10 w-10 items-center justify-center rounded-full bg-white/20 text-white transition-all delay-150 duration-300 duration-500 hover:scale-110 hover:rotate-90 hover:bg-white/30"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Video container */}
            <div className="hover:shadow-3xl relative aspect-video w-full transform overflow-hidden rounded-2xl bg-slate-800 shadow-2xl transition-all duration-300 hover:scale-[1.02]">
              <video
                className="h-full w-full object-cover"
                controls
                autoPlay
                muted
                playsInline
              >
                <source src={"/Luxury.mp4"} type="video/mp4" />
                <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-white/20">
                    <Play className="ml-1 h-8 w-8 text-white" />
                  </div>
                  <h3 className="mb-2 text-xl font-semibold">
                    Your browser does not support the video tag
                  </h3>
                  <p className="max-w-md text-center text-white/70">
                    Please update your browser to view this video
                  </p>
                </div>
              </video>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default LuxuryExperience;
