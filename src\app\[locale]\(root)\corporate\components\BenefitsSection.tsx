import { DollarSign, TrendingUp, <PERSON>ting<PERSON>, LucideProps } from "lucide-react";
import { useTranslations } from "next-intl";

interface Benefit {
  icon: React.ComponentType<LucideProps>;
  title: string;
  description: string;
  image: string;
}

const BenefitsSection: React.FC = () => {
  const t = useTranslations("corporate");

  const benefits: Benefit[] = [
    {
      icon: DollarSign,
      title: t("whyStella.costSaving.title"),
      description: t("whyStella.costSaving.description"),
      image: "/CorporateStay/cost_saving.webp",
    },
    {
      icon: TrendingUp,
      title: t("whyStella.productivity.title"),
      description: t("whyStella.productivity.description"),
      image: "/CorporateStay/increase_productivity.webp",
    },
    {
      icon: Settings,
      title: t("whyStella.flexibility.title"),
      description: t("whyStella.flexibility.description"),
      image: "/CorporateStay/Flexible-solutions.webp",
    },
  ];

  return (
    <section className="bg-muted/30 py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("whyStella.title")}
          </h2>
        </div>

        <div className="space-y-16">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className={`grid items-center gap-12 lg:grid-cols-2 ${
                index % 2 === 1 ? "lg:grid-flow-col-dense" : ""
              }`}
            >
              <div className={index % 2 === 1 ? "lg:col-start-2" : ""}>
                <div className="mb-6 flex items-center">
                  <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-xl bg-[#279fc7] shadow-lg">
                    <benefit.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-foreground text-2xl font-bold">
                    {benefit.title}
                  </h3>
                </div>
                <p className="text-muted-foreground text-lg leading-relaxed">
                  {benefit.description}
                </p>
              </div>
              <div className={index % 2 === 1 ? "lg:col-start-1" : ""}>
                <img
                  src={benefit.image}
                  alt={benefit.title}
                  className="h-auto w-full rounded-2xl shadow-xl transition-transform duration-300 hover:scale-105"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
