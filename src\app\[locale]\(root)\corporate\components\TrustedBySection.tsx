import { useTranslations } from "next-intl";

const TrustedBySection: React.FC = () => {
  const t = useTranslations("corporate");

  return (
    <section className="bg-muted/20 py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("trustedBy.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
            {t("trustedBy.description")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default TrustedBySection;
