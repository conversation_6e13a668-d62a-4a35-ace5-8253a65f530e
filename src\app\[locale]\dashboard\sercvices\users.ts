import api from "@/lib/axios";
import { User } from "../types/users";
import { AxiosError } from "axios";

export type GetUsersParams = {
  pageNumber: number;
  pageSize: number;
  isApproved?: boolean;
};

type GetUsersResponse =
  | {
      success: true;
      data: {
        items: User[];
        totalCount: number;
        pageNumber: number;
        pageSize: number;
        totalPages: number;
        hasPreviousPage: boolean;
        hasNextPage: boolean;
      };
    }
  | {
      success: false;
      error: string;
    };

export const getUsers = async ({
  pageNumber = 1,
  pageSize = 15,
  isApproved,
}: GetUsersParams): Promise<GetUsersResponse> => {
  try {
    const res = await api("/Users/<USER>", {
      params: {
        pageNumber,
        pageSize,
        ...(isApproved !== undefined ? { isApproved } : {}),
      },
    });

    return {
      success: true,
      data: res.data,
    };
  } catch (err) {
    console.error(err);
    if (err instanceof AxiosError) {
      if (err.response?.status === 400) {
        return { success: false, error: "طلب غير صالح (400)" };
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        return { success: false, error: "غير مصرح (401)" };
      }
    }

    return {
      success: false,
      error: "حدث خطأ غير متوقع",
    };
  }
};
