import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Building2, Users, Globe, Star, Sparkles, Shield } from "lucide-react";

interface Stat {
  number: string;
  label: string;
  icon: React.ElementType;
}

const HeroSection: React.FC = () => {
  const t = useTranslations();

  const stats: Stat[] = [
    {
      number: "50K+",
      label: t("about.stats.happyGuests"),
      icon: Users,
    },
    {
      number: "100+",
      label: t("about.stats.cities"),
      icon: Globe,
    },
    {
      number: "4.9",
      label: t("about.stats.rating"),
      icon: Star,
    },
    {
      number: "24/7",
      label: t("about.stats.support"),
      icon: Shield,
    },
  ];

  return (
    <section className="from-background via-brand-warm/30 to-brand-teal-light/20 relative overflow-hidden bg-gradient-to-br py-24">
      {/* Background decorations */}
      <div className="absolute top-20 right-10 opacity-5">
        <Building2 className="text-primary h-32 w-32" />
      </div>
      <div className="absolute bottom-20 left-10 opacity-5">
        <Sparkles className="text-primary h-24 w-24" />
      </div>

      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
            <Building2 className="text-primary mr-2 h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              {t("about.subtitle")}
            </span>
          </div>
          <h1 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
            {t("about.title")}
          </h1>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl leading-relaxed">
            {t("about.description")}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="mb-16 grid grid-cols-2 gap-6 md:grid-cols-4">
          {stats.map((stat, index) => (
            <Card
              key={index}
              className="shadow-soft from-background to-brand-warm/20 hover:shadow-elevated border-0 bg-gradient-to-br transition-all duration-300"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-6 text-center">
                <stat.icon className="text-primary mx-auto mb-3 h-8 w-8" />
                <div className="text-foreground mb-1 text-2xl font-bold md:text-3xl">
                  {stat.number}
                </div>
                <div className="text-muted-foreground text-sm">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
