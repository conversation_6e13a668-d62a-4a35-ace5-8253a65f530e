import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { createAuthValidations } from "@/lib/validations/auth";
import { useLanguage } from "@/providers/LanguageContext";
import { zodResolver } from "@hookform/resolvers/zod";
import { Mail } from "lucide-react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import PasswordInput from "../ui/password-input";
import { useAuthStore } from "@/store/auth";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { useAuthDialogStore } from "@/store/authDialogStore";
import EmailInput from "../ui/email-input";

export default function SigninForm() {
  const t = useTranslations();
  const { isRTL } = useLanguage();
  const { signinSchema } = createAuthValidations(t);
  const { login } = useAuthStore();
  const { closeDialog, setMessage } = useAuthDialogStore();

  const form = useForm<z.infer<typeof signinSchema>>({
    resolver: zodResolver(signinSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof signinSchema>) => {
    try {
      const { statusCode } = await login(values.email, values.password);

      if (statusCode === 400 || statusCode === 401) {
        form.setError("root", {
          message: t("login.error"),
        });
      }

      if (statusCode === 200) {
        closeDialog();
        // Clear any messages in auth dialog after login
        setMessage(null);
      }
    } catch (error) {
      console.error(error);
      form.setError("root", {
        message: t("errors.generalError"),
      });
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        noValidate
        className="space-y-4"
      >
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>{t("auth.email")}</FormLabel>
              <FormControl>
                <EmailInput placeholder={t("auth.enterEmail")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem className="space-y-2">
              <FormLabel>{t("auth.password")}</FormLabel>
              <FormControl>
                <PasswordInput {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {form.formState.errors.root?.message && (
          <Alert variant="destructive" className="border-red-500 bg-red-200/50">
            <AlertTitle>{isRTL ? "خطأ" : "Error"}</AlertTitle>
            <AlertDescription>
              {form.formState.errors.root?.message}
            </AlertDescription>
          </Alert>
        )}

        <Button
          type="submit"
          className="w-full bg-[#279fc7] text-white hover:bg-[#279fc7]/90"
          disabled={form.formState.isSubmitting}
        >
          {form.formState.isSubmitting ? t("auth.signingIn") : t("auth.signIn")}
        </Button>

        {/* Social Login */}
        <div className="space-y-3">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2">{isRTL ? "أو" : "Or"}</span>
            </div>
          </div>

          <Button
            type="button"
            variant="outline"
            className="w-full border-gray-300 hover:bg-gray-50"
          >
            <svg
              className={`h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`}
              viewBox="0 0 24 24"
            >
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            {isRTL ? "تسجيل الدخول مع Google" : "Login with Google"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
