"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/i18n/navigation";
import { Globe } from "lucide-react";
import { useLocale } from "next-intl";

export default function LangSwitcher() {
  const locale = useLocale();

  const nextLocale = locale === "ar" ? "en" : "ar";

  return (
    <Button
      variant="ghost"
      size="sm"
      className="rounded-lg px-3 py-2 transition-colors hover:bg-[#279fc7]/10 hover:text-[#279fc7]"
      asChild
    >
      <Link href="/" locale={nextLocale}>
        <Globe className="h-[1.2rem] w-[1.2rem]" />
        <span className="lg:hidden xl:block">{locale === "ar" ? "English" : "العربية"}</span>
      </Link>
    </Button>
  );
}
