import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createCorporatePricingRequestApi } from "@/api/corporate";
import { toast } from "sonner";

export const useCreateCorporate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCorporatePricingRequestApi,
    onSuccess: () => {
      toast.success("Corporate pricing request created successfully");
      queryClient.invalidateQueries({ queryKey: ["corporatePricingRequests"] });
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to create corporate pricing request",
      );
    },
  });
};
