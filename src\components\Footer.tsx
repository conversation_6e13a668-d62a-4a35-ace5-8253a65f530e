"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Instagram, Twitter, Linkedin, Facebook } from "lucide-react";
import { useTranslations } from "next-intl";
import { useLanguage } from "@/providers/LanguageContext";
import Link from "next/link";

const Footer = () => {
  const t = useTranslations();
  const { isRTL } = useLanguage();

  return (
    <footer className="bg-background text-foreground py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div
              className={`mb-6 flex items-center ${
                isRTL ? "space-x-2 space-x-reverse" : "space-x-2"
              }`}
            >
              <div className="bg-gradient-ocean flex h-8 w-8 items-center justify-center rounded-lg">
                <span className="text-sm font-bold text-white">H</span>
              </div>
              <span className="text-xl font-semibold">hala</span>
            </div>
            <p className="text-foreground/80 mb-6 leading-relaxed">
              {t("footer.description")}
            </p>
            <div
              className={`flex ${
                isRTL ? "space-x-4 space-x-reverse" : "space-x-4"
              }`}
            >
              <Button
                variant="ghost"
                size="icon"
                className="text-foreground/80 hover:text-foreground hover:bg-foreground/10"
              >
                <Instagram className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-foreground/80 hover:text-foreground hover:bg-foreground/10"
              >
                <Twitter className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-foreground/80 hover:text-foreground hover:bg-foreground/10"
              >
                <Linkedin className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-foreground/80 hover:text-foreground hover:bg-foreground/10"
              >
                <Facebook className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Company */}
          <div>
            <h3 className="mb-6 text-lg font-semibold">
              {t("footer.company")}
            </h3>
            <ul className="space-y-4">
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.aboutUs")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.careers")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.press")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.blog")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.investorRelations")}
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="mb-6 text-lg font-semibold">
              {t("footer.support")}
            </h3>
            <ul className="space-y-4">
              <li>
                <Link
                  href="/faq"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("faq.title")}
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.helpCenter")}
                </a>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.contactUs")}
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.safetySecurity")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.communityGuidelines")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-foreground/80 hover:text-foreground transition-colors"
                >
                  {t("footer.accessibility")}
                </a>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="mb-6 text-lg font-semibold">
              {t("footer.stayUpdated")}
            </h3>
            <p className="text-foreground/80 mb-4">
              {t("footer.newsletterDescription")}
            </p>
            <div className="space-y-3">
              <Input
                type="email"
                placeholder={t("footer.emailPlaceholder")}
                className="bg-foreground/10 border-foreground/20 text-foreground placeholder:text-foreground/60 focus:bg-foreground/20"
              />
              <Button className="bg-brand-teal hover:bg-brand-teal-dark w-full text-white">
                {t("footer.subscribe")}
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-foreground/20 border-t pt-8">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <p className="text-foreground/60 text-sm">
              {t("footer.copyright")}
            </p>
            <div
              className={`flex text-sm ${
                isRTL ? "space-x-6 space-x-reverse" : "space-x-6"
              }`}
            >
              <a
                href="#"
                className="text-foreground/60 hover:text-foreground transition-colors"
              >
                {t("footer.privacyPolicy")}
              </a>
              <a
                href="#"
                className="text-foreground/60 hover:text-foreground transition-colors"
              >
                {t("footer.termsOfService")}
              </a>
              <a
                href="#"
                className="text-foreground/60 hover:text-foreground transition-colors"
              >
                {t("footer.cookiePolicy")}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
