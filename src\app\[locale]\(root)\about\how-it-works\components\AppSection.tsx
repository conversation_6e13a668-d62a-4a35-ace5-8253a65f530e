import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "@/i18n/navigation";
import { CalendarCheck, Key, CalendarDays, Headphones } from "lucide-react";

interface AppFeature {
  icon: React.ElementType;
  key: string;
}

const AppSection: React.FC = () => {
  const t = useTranslations("howItWorks.app");

  const appFeatures: AppFeature[] = [
    { icon: CalendarCheck, key: "booking" },
    { icon: Key, key: "checkIn" },
    { icon: CalendarDays, key: "trips" },
    { icon: Headphones, key: "support" },
  ];

  return (
    <section className="bg-background py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          <div className="text-center lg:text-left">
            <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
              {t("title")}
            </h2>
            <p className="text-muted-foreground mb-8 text-xl">
              {t("subtitle")}
            </p>
            <div className="mb-8 grid grid-cols-2 gap-6">
              {appFeatures.map((feature) => (
                <div key={feature.key} className="flex items-center">
                  <div className="bg-primary/10 mr-4 flex h-10 w-10 items-center justify-center rounded-full">
                    <feature.icon className="text-primary h-5 w-5" />
                  </div>
                  <span className="text-foreground font-semibold">
                    {t(`features.${feature.key}`)}
                  </span>
                </div>
              ))}
            </div>
            <Link href="/download-app">
              <Button size="lg" className="bg-primary hover:bg-primary/90">
                {t("cta")}
              </Button>
            </Link>
          </div>
          <div className="relative h-96 lg:h-auto">
            <img
              src="/path-to-your-app-mockup.png"
              alt="HALA App Mockup"
              className="absolute inset-0 h-full w-full object-contain"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppSection;
